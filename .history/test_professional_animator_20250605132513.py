#!/usr/bin/env python3
"""
专业游戏动画师系统测试
Professional Game Animator System Test
"""

import asyncio
import json
import sys
import os

# 添加backend到路径
sys.path.append('backend')

from animation.models import AnimationRequest
from animation.professional_pipeline import ProfessionalAnimationPipeline


async def test_example_request():
    """测试示例请求：生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步"""
    
    print("🎬 专业游戏动画师系统测试")
    print("=" * 50)
    
    # 创建管道
    pipeline = ProfessionalAnimationPipeline()
    
    # 测试请求
    test_cases = [
        {
            "text": "生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步",
            "description": "复杂特技动作序列"
        },
        {
            "text": "角色慢慢走向前方，然后挥手打招呼",
            "description": "基础移动+手势"
        },
        {
            "text": "快速冲刺攻击，然后防御姿态",
            "description": "战斗动作组合"
        },
        {
            "text": "向前走三步",
            "description": "简单移动"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🎯 测试案例 {i}: {test_case['description']}")
        print(f"📝 输入: {test_case['text']}")

        # 创建请求
        request = AnimationRequest(
            text=test_case['text'],
            character_id=f"test_character_{i}",
            quality_target="game_ready",
            frame_rate=30,
            export_format="fbx"
        )
        
        try:
            # 处理请求
            response = await pipeline.process_animation_request(request)
            
            if response.success:
                print("✅ 成功生成动画")
                print(f"📊 动作数量: {len(response.processed_actions)}")
                print(f"🎬 动作列表: {', '.join(response.processed_actions)}")
                print(f"⏱️  总时长: {response.animation_sequence.total_duration:.2f}秒")
                print(f"🎞️  总帧数: {response.animation_sequence.total_frames}")
                print(f"📁 FBX文件: {response.fbx_file_path}")
                print(f"⚡ 处理时间: {response.processing_time:.2f}秒")
                
                # 显示质量报告
                if response.quality_report:
                    print(f"📈 复杂度评分: {response.quality_report.get('complexity_score', 0)}")
                    if response.quality_report.get('recommendations'):
                        print("💡 优化建议:")
                        for rec in response.quality_report['recommendations']:
                            print(f"   - {rec}")
            else:
                print("❌ 动画生成失败")
                print(f"🚫 错误信息: {response.error_message}")
                
        except Exception as e:
            print(f"💥 测试失败: {e}")
        
        print("-" * 40)


async def test_nlu_only():
    """仅测试NLU功能"""
    print("\n🧠 测试自然语言理解功能")
    print("=" * 30)
    
    from animation.professional_nlu import ProfessionalAnimatorNLU
    
    nlu = ProfessionalAnimatorNLU()
    
    test_text = "生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步"
    
    request = AnimationRequest(
        text=test_text,
        character_id="test_nlu",
        animator_level="intermediate"
    )
    
    try:
        response = await nlu.process_natural_language(request)
        
        if response.success:
            print("✅ NLU处理成功")
            print(f"📝 原始文本: {response.original_text}")
            print(f"🔄 处理后文本: {response.processed_text}")
            print(f"🎭 识别的动作:")
            
            for i, action in enumerate(response.animation_sequence.actions, 1):
                print(f"   {i}. {action.name} ({action.type.value})")
                print(f"      - 持续时间: {action.duration}秒")
                print(f"      - 强度: {action.intensity.value}")
                if action.direction:
                    print(f"      - 方向: {action.direction.value}")
                if action.angle:
                    print(f"      - 角度: {action.angle}度")
                if action.steps:
                    print(f"      - 步数: {action.steps}")
                if action.primary_body_parts:
                    parts = [part.value for part in action.primary_body_parts]
                    print(f"      - 身体部位: {', '.join(parts)}")
        else:
            print("❌ NLU处理失败")
            print(f"🚫 错误: {response.error_message}")
            
    except Exception as e:
        print(f"💥 NLU测试失败: {e}")


def test_animator_functions():
    """测试动画师功能"""
    print("\n👨‍🎨 测试动画师功能")
    print("=" * 25)
    
    from animation.animator_functions import JuniorAnimator, IntermediateAnimator
    from animation.models import Direction, AnimationIntensity
    
    # 测试初级动画师
    print("🎓 初级动画师功能测试:")
    junior = JuniorAnimator()
    
    try:
        # 测试走路循环
        walk_action = junior.create_walk_cycle(
            direction=Direction.FORWARD,
            intensity=AnimationIntensity.NORMAL
        )
        print(f"✅ 走路循环: {walk_action.name}, 时长: {walk_action.duration}秒")
        
        # 测试跳跃
        jump_action = junior.create_jump_animation(
            direction=Direction.UP,
            height=1.5
        )
        print(f"✅ 跳跃动画: {jump_action.name}, 时长: {jump_action.duration}秒")
        
        # 测试待机
        idle_action = junior.create_idle_animation(style="breathing")
        print(f"✅ 待机动画: {idle_action.name}, 时长: {idle_action.duration}秒")
        
    except Exception as e:
        print(f"❌ 初级动画师测试失败: {e}")
    
    # 测试中级动画师
    print("\n🎖️ 中级动画师功能测试:")
    intermediate = IntermediateAnimator()
    
    try:
        # 测试后空翻720度
        backflip_action = intermediate.create_backflip_720()
        print(f"✅ 后空翻720度: {backflip_action.name}, 时长: {backflip_action.duration}秒, 角度: {backflip_action.angle}度")
        
        # 测试战斗攻击
        attack_action = intermediate.create_combat_attack(
            attack_type="punch",
            hand=None
        )
        print(f"✅ 拳击攻击: {attack_action.name}, 时长: {attack_action.duration}秒")
        
        # 测试复杂转身
        turn_action = intermediate.create_complex_turn(
            angle=180,
            steps=2
        )
        print(f"✅ 复杂转身: {turn_action.name}, 角度: {turn_action.angle}度, 步数: {turn_action.steps}")
        
        # 测试面部表情
        expression_action = intermediate.create_facial_expression("smile")
        print(f"✅ 面部表情: {expression_action.name}, 时长: {expression_action.duration}秒")
        
    except Exception as e:
        print(f"❌ 中级动画师测试失败: {e}")


def main():
    """主测试函数"""
    print("🚀 启动专业游戏动画师系统测试")
    print("=" * 60)
    
    # 检查依赖
    try:
        import spacy
        import transformers
        import numpy
        print("✅ 核心依赖检查通过")
    except ImportError as e:
        print(f"❌ 依赖缺失: {e}")
        print("请运行: poetry install")
        return
    
    # 运行测试
    try:
        # 测试动画师功能（同步）
        test_animator_functions()
        
        # 测试NLU和完整管道（异步）
        asyncio.run(test_nlu_only())
        asyncio.run(test_example_request())
        
        print("\n🎉 测试完成！")
        print("=" * 20)
        print("📋 测试总结:")
        print("✅ 动画师功能测试")
        print("✅ 自然语言理解测试") 
        print("✅ 完整管道测试")
        print("\n💡 下一步:")
        print("1. 启动API服务器: poetry run uvicorn backend.app:app --reload")
        print("2. 访问文档: http://localhost:8000/docs")
        print("3. 测试专业动画师端点: /animation/generate")
        
    except Exception as e:
        print(f"💥 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
