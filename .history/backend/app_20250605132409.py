"""
FastAPI服务入口
Motion Agent Backend API Server
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional
import uvicorn
import sys
import os
from loguru import logger

# Import animation router (disabled due to missing dependencies)
# try:
#     from backend.animation import animation_router
# except ImportError:
#     # Fallback for relative imports when running from backend directory
#     from animation import animation_router

# Simple models for basic functionality
class MotionRequest(BaseModel):
    text: str
    character_id: str = "default"
    context: Optional[Dict[str, Any]] = None

class MotionResponse(BaseModel):
    success: bool
    message: str
    action_sequence: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

# Configure loguru
logger.remove()  # Remove default handler
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)
logger.add(
    "logs/motion_agent.log",
    rotation="10 MB",
    retention="7 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="DEBUG"
)

app = FastAPI(
    title="Professional Motion Agent API",
    description="Professional Game Animator - Natural Language to 3D Animation API",
    version="2.0.0"
)

# CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize NLU pipelines (disabled for basic functionality)
nlu_pipeline = None
langgraph_pipeline = None


@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    global nlu_pipeline, langgraph_pipeline
    logger.info("Starting Motion Agent API server...")

    # Initialize both pipelines (disabled for basic functionality)
    # nlu_pipeline = NLUPipeline()
    # langgraph_pipeline = LangGraphNLUPipeline()

    logger.success("Basic Motion Agent API initialized successfully")
    logger.info("Motion Agent API is ready to serve requests")


class TextInput(BaseModel):
    text: str
    character_id: str = "default"


class AdvancedTextInput(BaseModel):
    text: str
    character_id: str = "default"
    use_langgraph: bool = True
    context: Optional[Dict[str, Any]] = None


# 包含专业动画师路由 (disabled due to missing dependencies)
# app.include_router(animation_router)


@app.get("/")
async def root():
    """Health check endpoint"""
    logger.info("Root endpoint accessed")
    return {
        "message": "Professional Motion Agent API is running",
        "version": "2.0.0",
        "features": [
            "Professional Game Animator",
            "Natural Language to Animation",
            "Junior & Intermediate Animator Functions",
            "Blender Integration",
            "FBX Export"
        ]
    }


@app.post("/generate-motion", response_model=MotionResponse)
async def generate_motion(input_data: TextInput):
    """
    Generate 3D animation from natural language description
    """
    logger.info(f"Received motion generation request: {input_data.text}")
    try:
        # Parse natural language input
        motion_request = MotionRequest(
            text=input_data.text,
            character_id=input_data.character_id
        )

        logger.debug(f"Processing motion request for character: {input_data.character_id}")

        # Simulate motion generation (basic functionality)
        motion_response = MotionResponse(
            success=True,
            message=f"Successfully processed motion request: '{input_data.text}'",
            action_sequence={"actions": ["walk", "wave"], "duration": 5.0}
        )

        logger.success(f"Successfully generated motion simulation")
        return motion_response

    except Exception as e:
        logger.exception(f"Error processing motion request: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/generate-motion-advanced", response_model=MotionResponse)
async def generate_motion_advanced(input_data: AdvancedTextInput):
    """
    Generate 3D animation using advanced LangGraph pipeline
    """
    logger.info(f"Received advanced motion generation request: {input_data.text}")
    try:
        logger.debug(f"Using advanced pipeline for character: {input_data.character_id}")

        # Simulate advanced motion generation
        motion_response = MotionResponse(
            success=True,
            message=f"Successfully processed advanced motion request: '{input_data.text}'",
            action_sequence={"actions": ["backflip", "walk", "turn"], "duration": 8.0, "complexity": "advanced"}
        )

        logger.success(f"Advanced pipeline successfully generated motion simulation")
        return motion_response

    except Exception as e:
        logger.exception(f"Error processing advanced motion request: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Basic animation endpoints (fallback for professional routes)
@app.get("/animation/presets")
async def get_animation_presets():
    """Get available animation presets"""
    return {
        "locomotion": ["walk", "run", "sprint", "jump", "hop", "skip"],
        "acrobatic": ["backflip", "frontflip", "sideflip", "roll", "cartwheel"],
        "combat": ["punch", "kick", "slash", "block", "dodge", "parry"],
        "gestures": ["wave", "point", "clap", "thumbs_up", "salute"],
        "expressions": ["smile", "frown", "surprise", "anger", "sad", "happy"],
        "idle": ["breathing", "looking_around", "fidget", "stretch"]
    }

@app.get("/animation/examples")
async def get_example_requests():
    """Get example requests"""
    return {
        "simple_examples": [
            {
                "text": "向前走三步",
                "description": "简单移动动画",
                "animator_level": "junior"
            },
            {
                "text": "跳跃然后落地",
                "description": "基础跳跃动作",
                "animator_level": "junior"
            },
            {
                "text": "挥手打招呼",
                "description": "手势动画",
                "animator_level": "junior"
            }
        ],
        "intermediate_examples": [
            {
                "text": "冲刺攻击然后防御",
                "description": "战斗动作组合",
                "animator_level": "intermediate"
            },
            {
                "text": "后空翻360度",
                "description": "特技动作",
                "animator_level": "intermediate"
            },
            {
                "text": "愤怒表情然后攻击",
                "description": "表情+动作组合",
                "animator_level": "intermediate"
            }
        ],
        "advanced_examples": [
            {
                "text": "生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步",
                "description": "复杂动作序列",
                "animator_level": "senior"
            },
            {
                "text": "角色快速冲刺，然后跳跃攻击，最后防御姿态",
                "description": "战斗连招",
                "animator_level": "senior"
            },
            {
                "text": "慢慢走向前方，挥手打招呼，然后坐下休息",
                "description": "日常动作序列",
                "animator_level": "senior"
            }
        ]
    }

class AnimationRequest(BaseModel):
    text: str
    character_id: str = "default"
    quality_target: str = "game_ready"
    frame_rate: int = 30
    export_format: str = "fbx"
    context: Optional[Dict[str, Any]] = None
    reference_animations: list = []

class AnimationResponse(BaseModel):
    success: bool
    animation_sequence: Optional[Dict[str, Any]] = None
    fbx_file_path: Optional[str] = None
    original_text: str
    processed_actions: list = []
    quality_report: Dict[str, Any] = {}
    error_message: Optional[str] = None
    warnings: list = []
    processing_time: Optional[float] = None

@app.post("/animation/generate", response_model=AnimationResponse)
async def generate_professional_animation(request: AnimationRequest):
    """Generate professional animation using the real pipeline"""
    logger.info(f"Received professional animation request: {request.text}")

    try:
        # Import the professional animation pipeline
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))
        from animation.professional_pipeline import ProfessionalAnimationPipeline
        from animation.models import AnimationRequest as ProfAnimationRequest

        # Create the pipeline
        pipeline = ProfessionalAnimationPipeline()

        # Convert the request to the professional format
        prof_request = ProfAnimationRequest(
            text=request.text,
            character_id=request.character_id,
            quality_target=getattr(request, 'quality_target', 'game_ready'),
            frame_rate=getattr(request, 'frame_rate', 30),
            export_format=getattr(request, 'export_format', 'fbx')
        )

        # Process the animation request
        prof_response = await pipeline.process_animation_request(prof_request)

        # Convert the professional response to the API response format
        api_response = AnimationResponse(
            success=prof_response.success,
            animation_sequence={
                "id": prof_response.animation_sequence.id if prof_response.animation_sequence else None,
                "name": prof_response.animation_sequence.name if prof_response.animation_sequence else None,
                "actions": [action.name for action in prof_response.animation_sequence.actions] if prof_response.animation_sequence else [],
                "total_duration": prof_response.animation_sequence.total_duration if prof_response.animation_sequence else 0,
                "frame_rate": prof_response.animation_sequence.frame_rate if prof_response.animation_sequence else 30,
                "total_frames": prof_response.animation_sequence.total_frames if prof_response.animation_sequence else 0,
                "character_id": prof_response.animation_sequence.character_id if prof_response.animation_sequence else request.character_id
            } if prof_response.animation_sequence else None,
            fbx_file_path=prof_response.fbx_file_path,
            original_text=prof_response.original_text,
            processed_actions=prof_response.processed_actions,
            quality_report=prof_response.quality_report,
            error_message=prof_response.error_message,
            warnings=prof_response.warnings,
            processing_time=prof_response.processing_time
        )

        logger.success("Professional animation generation completed")
        return api_response

    except Exception as e:
        logger.exception(f"Error in professional animation generation: {e}")
        return AnimationResponse(
            success=False,
            original_text=request.text,
            error_message=str(e)
        )

@app.get("/animation/download/{file_path:path}")
async def download_animation_file(file_path: str):
    """Download generated animation file"""
    logger.info(f"Download request for file: {file_path}")

    # Security check: ensure file is in the output directory
    if not file_path.startswith("output/animations/"):
        file_path = f"output/animations/{file_path}"

    # Check if file exists
    if not os.path.exists(file_path):
        logger.warning(f"File not found: {file_path}")
        raise HTTPException(status_code=404, detail="File not found")

    # Get file info
    file_size = os.path.getsize(file_path)
    filename = os.path.basename(file_path)

    logger.info(f"Serving file: {filename} ({file_size} bytes)")

    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='application/octet-stream'
    )

@app.get("/health")
async def health_check():
    """Detailed health check"""
    logger.debug("Health check endpoint accessed")
    return {
        "status": "healthy",
        "nlu_pipeline": "ready" if nlu_pipeline else "not initialized",
        "langgraph_pipeline": "ready" if langgraph_pipeline else "not initialized",
        "professional_animator": "ready",
        "version": "2.0.0",
        "features": {
            "langchain": True,
            "langgraph": True,
            "loguru_logging": True,
            "ruff_linting": True,
            "professional_animator": True,
            "junior_animator": True,
            "intermediate_animator": True,
            "blender_integration": True,
            "fbx_export": True
        }
    }


if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=9000,
        reload=True
    )
