#!/usr/bin/env python3
"""
完整工作流程测试
Complete Workflow Test
"""

import requests
import json
import time
import os


def test_api_generation_and_download():
    """测试API生成和下载功能"""
    print("🚀 测试完整的API工作流程")
    print("=" * 50)
    
    # API基础URL
    base_url = "http://localhost:9001"
    
    # 测试数据
    test_request = {
        "text": "向前走三步然后挥手",
        "character_id": "workflow_test",
        "quality_target": "game_ready",
        "frame_rate": 30,
        "export_format": "fbx"
    }
    
    print(f"📝 测试请求: {test_request['text']}")
    
    try:
        # 1. 发送动画生成请求
        print("\n🎬 步骤1: 发送动画生成请求")
        response = requests.post(
            f"{base_url}/animation/generate",
            json=test_request,
            timeout=60
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 动画生成成功!")
            
            # 显示结果信息
            if result.get("success"):
                print(f"🎯 处理的动作: {result.get('processed_actions', [])}")
                print(f"⏱️  处理时间: {result.get('processing_time', 0):.2f}秒")
                
                fbx_path = result.get("fbx_file_path")
                if fbx_path:
                    print(f"📁 FBX文件路径: {fbx_path}")
                    
                    # 2. 检查文件是否真的存在
                    print("\n📂 步骤2: 验证FBX文件存在")
                    if os.path.exists(fbx_path):
                        file_size = os.path.getsize(fbx_path)
                        print(f"✅ FBX文件确认存在: {file_size} bytes")
                        
                        # 3. 测试下载功能
                        print("\n⬇️  步骤3: 测试文件下载")
                        filename = os.path.basename(fbx_path)
                        download_url = f"{base_url}/animation/download/{filename}"
                        
                        print(f"🔗 下载URL: {download_url}")
                        
                        download_response = requests.get(download_url, timeout=30)
                        print(f"📊 下载响应状态码: {download_response.status_code}")
                        
                        if download_response.status_code == 200:
                            print("✅ 文件下载成功!")
                            print(f"📦 下载文件大小: {len(download_response.content)} bytes")
                            
                            # 验证下载的文件内容
                            if len(download_response.content) == file_size:
                                print("✅ 下载文件大小匹配原文件")
                            else:
                                print("⚠️  下载文件大小不匹配")
                                
                        else:
                            print(f"❌ 文件下载失败: {download_response.text}")
                            
                    else:
                        print(f"❌ FBX文件不存在: {fbx_path}")
                        
                else:
                    print("❌ 响应中没有FBX文件路径")
                    
            else:
                print(f"❌ 动画生成失败: {result.get('error_message', '未知错误')}")
                
        else:
            print(f"❌ API请求失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器")
        print("💡 请确保服务器正在运行: uv run uvicorn backend.app:app --host 0.0.0.0 --port 9001")
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")


def test_health_check():
    """测试健康检查端点"""
    print("\n🏥 测试健康检查")
    print("=" * 20)
    
    try:
        response = requests.get("http://localhost:9001/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ 服务器健康状态良好")
            print(f"📊 专业动画师状态: {health_data.get('professional_animator', 'unknown')}")
            print(f"🔧 Blender集成: {health_data.get('features', {}).get('blender_integration', False)}")
            print(f"📦 FBX导出: {health_data.get('features', {}).get('fbx_export', False)}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 健康检查错误: {e}")


def main():
    """主测试函数"""
    print("🎯 Motion Agent 完整工作流程测试")
    print("=" * 60)
    
    # 健康检查
    test_health_check()
    
    # 完整工作流程测试
    test_api_generation_and_download()
    
    print("\n🎉 测试完成!")
    print("=" * 20)
    print("💡 如果所有测试都通过，说明:")
    print("   ✅ API服务器正常运行")
    print("   ✅ 动画生成管道工作正常")
    print("   ✅ Blender集成正常")
    print("   ✅ FBX文件生成正常")
    print("   ✅ 文件下载功能正常")
    print("\n🚀 现在可以在前端界面中使用完整功能了!")


if __name__ == "__main__":
    main()
