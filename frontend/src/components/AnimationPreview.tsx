'use client';

import { MotionResponse, AnimationResponse } from '@/types/motion';
import { CheckCircle, AlertCircle, Clock, Download, FileText, Zap } from 'lucide-react';
import toast from 'react-hot-toast';

interface AnimationPreviewProps {
  response: MotionResponse | AnimationResponse | null;
  loading: boolean;
  error: string | null;
}

export default function AnimationPreview({ response, loading, error }: AnimationPreviewProps) {
  const handleDownload = async (filePath: string) => {
    try {
      // Extract filename from the full path
      const filename = filePath.split('/').pop() || 'animation.fbx';

      // Create download URL using the same base URL as the API
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9000';
      const downloadUrl = `${API_BASE_URL}/animation/download/${filename}`;

      toast.loading('Downloading FBX file...', { id: 'download' });

      // Fetch the file
      const response = await fetch(downloadUrl);

      if (!response.ok) {
        throw new Error(`Download failed: ${response.statusText}`);
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('FBX file downloaded successfully!', { id: 'download' });
    } catch (error) {
      console.error('Download error:', error);
      toast.error(`Download failed: ${error instanceof Error ? error.message : 'Unknown error'}`, { id: 'download' });
    }
  };
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <h3 className="text-lg font-semibold">Generating Animation...</h3>
        </div>
        <div className="space-y-3">
          <div className="animate-pulse h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="animate-pulse h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="animate-pulse h-4 bg-gray-200 rounded w-2/3"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center gap-3 mb-4">
          <AlertCircle className="w-6 h-6 text-red-500" />
          <h3 className="text-lg font-semibold text-red-700">Error</h3>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-700">{error}</p>
        </div>
      </div>
    );
  }

  if (!response) {
    return (
      <div className="bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 p-8 text-center">
        <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-600 mb-2">No Animation Generated</h3>
        <p className="text-gray-500">Enter a description and generate an animation to see results here.</p>
      </div>
    );
  }

  const isAdvancedResponse = 'animation_sequence' in response;

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center gap-3 mb-6">
        <CheckCircle className="w-6 h-6 text-green-500" />
        <h3 className="text-lg font-semibold">Animation Generated Successfully</h3>
      </div>

      {/* Success Message */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
        <p className="text-green-700 font-medium">{response.message}</p>
      </div>

      {/* Basic Motion Response */}
      {!isAdvancedResponse && response.action_sequence && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="w-4 h-4 text-blue-600" />
                <h4 className="font-medium text-blue-900">Actions</h4>
              </div>
              <div className="flex flex-wrap gap-2">
                {response.action_sequence.actions.map((action, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                  >
                    {action}
                  </span>
                ))}
              </div>
            </div>

            <div className="bg-purple-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="w-4 h-4 text-purple-600" />
                <h4 className="font-medium text-purple-900">Duration</h4>
              </div>
              <p className="text-2xl font-bold text-purple-800">
                {response.action_sequence.duration}s
              </p>
              {response.action_sequence.complexity && (
                <p className="text-sm text-purple-600 mt-1">
                  Complexity: {response.action_sequence.complexity}
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Advanced Animation Response */}
      {isAdvancedResponse && (
        <div className="space-y-6">
          {/* Animation Sequence Info */}
          {response.animation_sequence && (
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
              <h4 className="font-semibold text-gray-900 mb-4">Animation Sequence Details</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Duration</p>
                  <p className="text-lg font-semibold">{response.animation_sequence.total_duration}s</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Frame Rate</p>
                  <p className="text-lg font-semibold">{response.animation_sequence.frame_rate} FPS</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Frames</p>
                  <p className="text-lg font-semibold">{response.animation_sequence.total_frames}</p>
                </div>
              </div>
            </div>
          )}

          {/* Processed Actions */}
          {response.processed_actions && response.processed_actions.length > 0 && (
            <div className="bg-green-50 rounded-lg p-4">
              <h4 className="font-medium text-green-900 mb-3">Processed Actions</h4>
              <div className="flex flex-wrap gap-2">
                {response.processed_actions.map((action, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm"
                  >
                    {action}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* FBX File Download */}
          {response.fbx_file_path && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Download className="w-5 h-5 text-orange-600" />
                  <div>
                    <h4 className="font-medium text-orange-900">FBX File Ready</h4>
                    <p className="text-sm text-orange-700">{response.fbx_file_path}</p>
                  </div>
                </div>
                <button
                  onClick={() => handleDownload(response.fbx_file_path)}
                  className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                >
                  Download
                </button>
              </div>
            </div>
          )}

          {/* Quality Report */}
          {response.quality_report && Object.keys(response.quality_report).length > 0 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">Quality Report</h4>
              <pre className="text-sm text-gray-700 bg-white p-3 rounded border overflow-x-auto">
                {JSON.stringify(response.quality_report, null, 2)}
              </pre>
            </div>
          )}

          {/* Processing Time */}
          {response.processing_time && (
            <div className="text-center text-sm text-gray-500">
              Processing completed in {response.processing_time.toFixed(2)} seconds
            </div>
          )}

          {/* Warnings */}
          {response.warnings && response.warnings.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-900 mb-2">Warnings</h4>
              <ul className="list-disc list-inside space-y-1">
                {response.warnings.map((warning, index) => (
                  <li key={index} className="text-yellow-800 text-sm">{warning}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
