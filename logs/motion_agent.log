2025-06-05 10:55:03 | INFO     | backend.app:startup_event:80 - Starting Motion Agent API server...
2025-06-05 10:55:03 | SUCCESS  | backend.app:startup_event:86 - Basic Motion Agent API initialized successfully
2025-06-05 10:55:03 | INFO     | backend.app:startup_event:87 - Motion Agent API is ready to serve requests
2025-06-05 10:55:06 | INFO     | backend.app:root:109 - Root endpoint accessed
2025-06-05 10:56:24 | INFO     | backend.app:generate_motion:128 - Received motion generation request: 慢慢走向前方，然后挥手打招呼
2025-06-05 10:56:24 | DEBUG    | backend.app:generate_motion:136 - Processing motion request for character: test_character
2025-06-05 10:56:24 | SUCCESS  | backend.app:generate_motion:145 - Successfully generated motion simulation
2025-06-05 10:56:54 | INFO     | backend.app:root:109 - Root endpoint accessed
2025-06-05 10:56:59 | INFO     | backend.app:root:109 - Root endpoint accessed
2025-06-05 10:57:05 | DEBUG    | backend.app:health_check:180 - Health check endpoint accessed
2025-06-05 10:58:04 | INFO     | backend.app:startup_event:80 - Starting Motion Agent API server...
2025-06-05 10:58:04 | SUCCESS  | backend.app:startup_event:86 - Basic Motion Agent API initialized successfully
2025-06-05 10:58:04 | INFO     | backend.app:startup_event:87 - Motion Agent API is ready to serve requests
2025-06-05 10:58:14 | INFO     | backend.app:root:109 - Root endpoint accessed
2025-06-05 11:07:37 | INFO     | backend.app:startup_event:80 - Starting Motion Agent API server...
2025-06-05 11:07:37 | SUCCESS  | backend.app:startup_event:86 - Basic Motion Agent API initialized successfully
2025-06-05 11:07:37 | INFO     | backend.app:startup_event:87 - Motion Agent API is ready to serve requests
2025-06-05 11:22:51 | DEBUG    | backend.app:health_check:180 - Health check endpoint accessed
2025-06-05 11:22:51 | DEBUG    | backend.app:health_check:180 - Health check endpoint accessed
2025-06-05 11:23:22 | DEBUG    | backend.app:health_check:180 - Health check endpoint accessed
2025-06-05 11:25:56 | INFO     | app:startup_event:80 - Starting Motion Agent API server...
2025-06-05 11:25:56 | SUCCESS  | app:startup_event:86 - Basic Motion Agent API initialized successfully
2025-06-05 11:25:56 | INFO     | app:startup_event:87 - Motion Agent API is ready to serve requests
2025-06-05 11:26:01 | DEBUG    | app:health_check:180 - Health check endpoint accessed
2025-06-05 11:27:43 | INFO     | app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 11:27:43 | SUCCESS  | app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 11:27:43 | INFO     | app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 11:27:47 | INFO     | app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 11:27:47 | SUCCESS  | app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 11:28:01 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:28:02 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:28:23 | INFO     | app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 11:28:23 | SUCCESS  | app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 11:28:31 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:28:32 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:28:32 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:28:42 | INFO     | app:generate_motion:120 - Received motion generation request: walk forward and wave
2025-06-05 11:28:42 | DEBUG    | app:generate_motion:128 - Processing motion request for character: test
2025-06-05 11:28:42 | SUCCESS  | app:generate_motion:137 - Successfully generated motion simulation
2025-06-05 11:28:49 | INFO     | app:generate_professional_animation:263 - Received professional animation request: 后空翻然后向前走三步
2025-06-05 11:28:49 | SUCCESS  | app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 11:29:01 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:29:02 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:29:33 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:30:03 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:30:11 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:30:33 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:31:03 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 11:31:06 | INFO     | app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 11:31:06 | SUCCESS  | app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 11:31:11 | DEBUG    | app:health_check:330 - Health check endpoint accessed
2025-06-05 12:45:28 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:45:28 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:45:28 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:45:30 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:45:33 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:45:34 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:45:34 | SUCCESS  | backend.app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 12:46:03 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:46:33 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:46:43 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:46:43 | SUCCESS  | backend.app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 12:47:03 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:47:34 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:47:49 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:47:49 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:47:49 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:48:03 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:48:04 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:48:04 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:48:04 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:48:35 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 向前走三步然后挥手
2025-06-05 12:48:35 | SUCCESS  | backend.app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 12:50:33 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 向前走三步
2025-06-05 12:50:33 | SUCCESS  | backend.app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 12:51:16 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:51:16 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:51:16 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:51:17 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:51:19 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:51:19 | SUCCESS  | backend.app:generate_professional_animation:316 - Professional animation simulation completed
2025-06-05 12:51:33 | DEBUG    | backend.app:health_check:330 - Health check endpoint accessed
2025-06-05 12:54:07 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:54:07 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:54:07 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:54:38 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 向前走三步然后挥手
2025-06-05 12:54:38 | ERROR    | backend.app:generate_professional_animation:290 - Error in professional animation generation: No module named 'animation'
Traceback (most recent call last):

  File "/Users/<USER>/project/llm/motion-agent/.venv/bin/uvicorn", line 10, in <module>
    sys.exit(main())
    │   │    └ <Command main>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function Command.main at 0x102d91620>
           └ <Command main>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x1026d28d0>
         │    └ <function Command.invoke at 0x102d91300>
         └ <Command main>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9001, 'app': 'backend.app:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'rel...
           │   │      │    │           └ <click.core.Context object at 0x1026d28d0>
           │   │      │    └ <function main at 0x102fdf4c0>
           │   │      └ <Command main>
           │   └ <function Context.invoke at 0x102d90540>
           └ <click.core.Context object at 0x1026d28d0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'host': '0.0.0.0', 'port': 9001, 'app': 'backend.app:app', 'uds': None, 'fd': None, 'reload': False, 'reload_dirs': (), 'rel...
           │         └ ()
           └ <function main at 0x102fdf4c0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/main.py", line 413, in main
    run(
    └ <function run at 0x102dd45e0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x102e1e3e0>
    └ <uvicorn.server.Server object at 0x102dc8050>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x102e1e480>
           │       │   └ <uvicorn.server.Server object at 0x102dc8050>
           │       └ <function run at 0x102b476a0>
           └ <module 'asyncio' from '/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/asyncio/__init__....
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x102f87100>
           │      └ <function Runner.run at 0x102b47a60>
           └ <asyncio.runners.Runner object at 0x102feeb90>
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-pac...
           │    │     └ <cyfunction Loop.run_until_complete at 0x102fcf370>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x102feeb90>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x103031e90>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4d0>>
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x1053b1f50>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x103031e90>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4d0>>
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4d0>>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x103257450>
          └ <fastapi.applications.FastAPI object at 0x1053b1f50>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x1053af4c0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x103394310>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x103257450>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x1053af4c0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x103394390>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x103394310>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x1053af4c0>
          │                            │    │    │     │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x1053ec6d0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x102fe7cd0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x103394390>
          └ <function wrap_app_handling_exceptions at 0x104d83f60>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1053af600>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <fastapi.routing.APIRouter object at 0x102fe7cd0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1053af600>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x102fe7cd0>>
          └ <fastapi.routing.APIRouter object at 0x102fe7cd0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1053af600>
          │     │      │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │     └ <function Route.handle at 0x104db1620>
          └ APIRoute(path='/animation/generate', name='generate_professional_animation', methods=['POST'])
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1053af600>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │    └ <function request_response.<locals>.app at 0x1053ad760>
          └ APIRoute(path='/animation/generate', name='generate_professional_animation', methods=['POST'])
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1053af600>
          │                            │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x1053ec690>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x1053af6a0>
          └ <function wrap_app_handling_exceptions at 0x104d83f60>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x1053af7e0>
          │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x1053ec4...
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9001), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x1053af6a0>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x1053ec690>
                     └ <function get_request_handler.<locals>.app at 0x1053ad620>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x104db1120>
  File "/Users/<USER>/project/llm/motion-agent/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': AnimationRequest(text='向前走三步然后挥手', character_id='test_api_fix', animator_level='intermediate', quality_target='ga...
                 │         └ <function generate_professional_animation at 0x1053ad580>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

> File "/Users/<USER>/project/llm/motion-agent/backend/app.py", line 267, in generate_professional_animation
    from animation.professional_pipeline import ProfessionalAnimationPipeline

ModuleNotFoundError: No module named 'animation'
2025-06-05 12:55:46 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:55:46 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:55:46 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:57:07 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:57:11 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 12:57:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:57:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:57:11 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 12:57:11 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 12:57:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:57:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:57:11 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 12:57:11 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:57:11 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 12:57:11 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 12:57:11 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 12:57:11 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749099431.json --output output/animations/animation_default_1749099431.fbx --format fbx
2025-06-05 12:57:12 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749099431.fbx
2025-06-05 12:57:12 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 1.08s
2025-06-05 12:57:12 | SUCCESS  | backend.app:generate_professional_animation:289 - Professional animation generation completed
2025-06-05 12:57:21 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 向前走三步然后挥手
2025-06-05 12:57:21 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 12:57:21 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 12:57:21 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 12:57:21 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 12:57:21 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:57:21 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:57:21 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 向前走三步然后挥手
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 12:57:21 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 向前走三步然后挥手
2025-06-05 12:57:21 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 2 actions
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 12:57:21 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 12:57:21 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749099441.json --output output/animations/animation_test_api_fix_1749099441.fbx --format fbx
2025-06-05 12:57:22 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_test_api_fix_1749099441.fbx
2025-06-05 12:57:22 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 0.46s
2025-06-05 12:57:22 | SUCCESS  | backend.app:generate_professional_animation:289 - Professional animation generation completed
2025-06-05 12:58:34 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 12:58:34 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 12:58:34 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 12:58:39 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:58:42 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 12:58:42 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:58:42 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:58:42 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 12:58:42 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 12:58:42 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:58:42 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 12:58:42 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 12:58:42 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 12:58:42 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 12:58:42 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 12:58:42 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 12:58:42 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749099522.json --output output/animations/animation_default_1749099522.fbx --format fbx
2025-06-05 12:58:43 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749099522.fbx
2025-06-05 12:58:43 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 0.96s
2025-06-05 12:58:43 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 13:03:38 | INFO     | backend.app:startup_event:72 - Starting Motion Agent API server...
2025-06-05 13:03:38 | SUCCESS  | backend.app:startup_event:78 - Basic Motion Agent API initialized successfully
2025-06-05 13:03:38 | INFO     | backend.app:startup_event:79 - Motion Agent API is ready to serve requests
2025-06-05 13:03:57 | INFO     | backend.app:generate_professional_animation:263 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:04:00 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:04:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:04:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:04:00 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:04:00 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:04:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:04:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:04:00 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 13:04:00 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:04:00 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 13:04:00 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 13:04:00 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 13:04:00 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749099840.json --output output/animations/animation_default_1749099840.fbx --format fbx
2025-06-05 13:04:01 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749099840.fbx
2025-06-05 13:04:01 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 0.97s
2025-06-05 13:04:01 | SUCCESS  | backend.app:generate_professional_animation:310 - Professional animation generation completed
2025-06-05 13:04:03 | DEBUG    | backend.app:health_check:324 - Health check endpoint accessed
2025-06-05 13:04:33 | DEBUG    | backend.app:health_check:324 - Health check endpoint accessed
2025-06-05 13:05:03 | DEBUG    | backend.app:health_check:324 - Health check endpoint accessed
2025-06-05 13:05:20 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:05:20 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:05:20 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:05:33 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:05:33 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:05:33 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:05:34 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:06:01 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:06:01 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:06:01 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:06:03 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:06:08 | INFO     | backend.app:generate_professional_animation:265 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:06:11 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:06:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:06:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:06:11 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:06:11 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:06:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:06:11 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:06:11 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 13:06:11 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:06:11 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 13:06:11 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 13:06:11 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 13:06:11 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749099971.json --output output/animations/animation_default_1749099971.fbx --format fbx
2025-06-05 13:06:12 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749099971.fbx
2025-06-05 13:06:12 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 0.94s
2025-06-05 13:06:12 | SUCCESS  | backend.app:generate_professional_animation:312 - Professional animation generation completed
2025-06-05 13:06:28 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:06:28 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:06:28 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:06:33 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:06:45 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:06:54 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:06:57 | INFO     | backend.app:generate_professional_animation:265 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:07:00 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:07:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:07:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:07:00 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:07:00 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:07:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:07:00 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:07:00 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 13:07:00 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:07:00 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 13:07:00 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 13:07:00 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 13:07:00 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749100020.json --output output/animations/animation_default_1749100020.fbx --format fbx
2025-06-05 13:07:01 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749100020.fbx
2025-06-05 13:07:01 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 0.93s
2025-06-05 13:07:01 | SUCCESS  | backend.app:generate_professional_animation:312 - Professional animation generation completed
2025-06-05 13:07:23 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:07:27 | INFO     | backend.app:generate_professional_animation:265 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:07:27 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:07:27 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:07:27 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:07:27 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:07:27 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:07:27 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:07:27 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 13:07:27 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:07:27 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 13:07:27 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 13:07:27 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 13:07:27 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749100047.json --output output/animations/animation_default_1749100047.fbx --format fbx
2025-06-05 13:07:28 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749100047.fbx
2025-06-05 13:07:28 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 1.01s
2025-06-05 13:07:28 | SUCCESS  | backend.app:generate_professional_animation:312 - Professional animation generation completed
2025-06-05 13:07:53 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:08:24 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:08:54 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:09:23 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:09:53 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:10:11 | INFO     | backend.app:root:103 - Root endpoint accessed
2025-06-05 13:10:24 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:10:36 | INFO     | backend.app:root:103 - Root endpoint accessed
2025-06-05 13:10:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:11:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:11:19 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:11:19 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:11:19 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:11:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:11:51 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:11:51 | INFO     | backend.app:download_animation_file:326 - Download request for file: animation_default_1749100020.fbx
2025-06-05 13:11:51 | INFO     | backend.app:download_animation_file:341 - Serving file: animation_default_1749100020.fbx (25852 bytes)
2025-06-05 13:12:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:12:24 | INFO     | backend.app:download_animation_file:326 - Download request for file: animation_default_1749100020.fbx
2025-06-05 13:12:24 | INFO     | backend.app:download_animation_file:341 - Serving file: animation_default_1749100020.fbx (25852 bytes)
2025-06-05 13:12:34 | INFO     | backend.app:generate_professional_animation:265 - Received professional animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:12:37 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:12:37 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:12:37 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:12:37 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:12:37 | WARNING  | animation.professional_nlu:_load_spacy_model:51 - No spaCy model found, using blank model
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:_load_sentiment_model:60 - Using rule-based sentiment analysis instead of large model
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:_load_action_classifier:70 - Using rule-based action classification instead of large model
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:__init__:37 - Professional Animator NLU initialized
2025-06-05 13:12:37 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:12:37 | INFO     | animation.animator_functions:__init__:28 - Junior Animator initialized
2025-06-05 13:12:37 | INFO     | animation.animator_functions:__init__:504 - Intermediate Animator initialized
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:__init__:44 - Professional Animation Pipeline initialized
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:process_animation_request:51 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:process_animation_request:55 - Step 1: Natural Language Understanding
2025-06-05 13:12:37 | INFO     | animation.professional_nlu:process_natural_language:178 - Processing animation request: 生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步
2025-06-05 13:12:37 | SUCCESS  | animation.professional_nlu:process_natural_language:207 - Successfully processed 3 actions
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:process_animation_request:62 - Step 2: Animator Function Processing
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:_apply_animator_functions:105 - Applying intermediate animator functions
2025-06-05 13:12:37 | INFO     | animation.animator_functions:create_walk_cycle:61 - Creating walk cycle: Direction.FORWARD, AnimationIntensity.NORMAL
2025-06-05 13:12:37 | INFO     | animation.animator_functions:create_complex_turn:632 - Creating complex turn: 180 degrees, 0 steps
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:process_animation_request:68 - Step 3: Generate Blender Animation Data
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:process_animation_request:72 - Step 4: Execute Blender Animation Generation
2025-06-05 13:12:37 | INFO     | animation.professional_pipeline:_execute_blender_generation:294 - Executing Blender command: /Applications/Blender.app/Contents/MacOS/Blender --background --python blender_scripts/generate_animation.py -- --input temp/animation_data/animation_data_1749100357.json --output output/animations/animation_default_1749100357.fbx --format fbx
2025-06-05 13:12:38 | SUCCESS  | animation.professional_pipeline:_execute_blender_generation:306 - Blender animation generated: output/animations/animation_default_1749100357.fbx
2025-06-05 13:12:38 | SUCCESS  | animation.professional_pipeline:process_animation_request:88 - Animation generated successfully in 0.88s
2025-06-05 13:12:38 | SUCCESS  | backend.app:generate_professional_animation:312 - Professional animation generation completed
2025-06-05 13:12:40 | INFO     | backend.app:download_animation_file:326 - Download request for file: animation_default_1749100357.fbx
2025-06-05 13:12:40 | INFO     | backend.app:download_animation_file:341 - Serving file: animation_default_1749100357.fbx (25852 bytes)
2025-06-05 13:12:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:13:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:13:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:14:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:14:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:15:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:15:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:15:55 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:15:55 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:15:55 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:16:08 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:16:08 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:16:08 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:16:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:16:20 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:16:20 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:16:20 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:16:51 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:16:51 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:16:51 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:17:02 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:17:02 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:17:02 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:17:28 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:17:28 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:17:28 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:17:42 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:17:57 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:17:57 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:17:57 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:18:16 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:18:16 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:18:16 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:18:27 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:18:27 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:18:27 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:18:42 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:18:46 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:18:46 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:18:46 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:19:06 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:19:06 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:19:06 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:19:23 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:19:23 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:19:23 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:19:42 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:19:44 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:19:44 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:19:44 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:20:06 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:20:06 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:20:06 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:20:12 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:20:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:20:28 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:20:28 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:20:28 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:20:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:21:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:21:45 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:21:45 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:21:45 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:21:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:21:56 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:21:56 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:21:56 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:22:07 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:22:07 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:22:07 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:22:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:22:20 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:22:20 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:22:20 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:22:32 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:22:32 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:22:32 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:22:48 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:22:50 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:22:50 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:22:50 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:23:05 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:23:05 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:23:05 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:23:18 | DEBUG    | backend.app:health_check:352 - Health check endpoint accessed
2025-06-05 13:23:20 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:23:20 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:23:20 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:23:33 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:23:33 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:23:33 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:23:45 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:23:45 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:23:45 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:23:57 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:23:57 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:23:57 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:24:09 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:24:09 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:24:09 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:24:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:25:01 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:25:01 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:25:01 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:25:13 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:25:13 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:25:13 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:25:21 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:25:21 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:25:21 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:25:42 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:25:42 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:25:42 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:25:42 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:25:51 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:25:51 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:25:51 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:26:10 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:26:10 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:26:10 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:26:25 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:26:27 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:26:57 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:27:10 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:27:10 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:27:10 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:27:22 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:27:22 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:27:22 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:27:27 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:27:43 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:27:43 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:27:43 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:27:57 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:28:04 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:28:04 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:28:04 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:28:27 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
2025-06-05 13:28:48 | INFO     | backend.app:startup_event:74 - Starting Motion Agent API server...
2025-06-05 13:28:48 | SUCCESS  | backend.app:startup_event:80 - Basic Motion Agent API initialized successfully
2025-06-05 13:28:48 | INFO     | backend.app:startup_event:81 - Motion Agent API is ready to serve requests
2025-06-05 13:28:57 | DEBUG    | backend.app:health_check:350 - Health check endpoint accessed
