#!/usr/bin/env python3
"""
测试下载端点
Test Download Endpoint
"""

import requests
import os


def test_download_endpoint():
    """测试下载端点功能"""
    print("🔗 测试下载端点")
    print("=" * 30)
    
    # 检查现有的FBX文件
    output_dir = "output/animations"
    if not os.path.exists(output_dir):
        print("❌ 输出目录不存在")
        return
    
    fbx_files = [f for f in os.listdir(output_dir) if f.endswith('.fbx')]
    if not fbx_files:
        print("❌ 没有找到FBX文件")
        return
    
    # 选择第一个文件进行测试
    test_file = fbx_files[0]
    print(f"📁 测试文件: {test_file}")
    
    # 获取原文件信息
    original_path = os.path.join(output_dir, test_file)
    original_size = os.path.getsize(original_path)
    print(f"📊 原文件大小: {original_size} bytes")
    
    # 测试下载
    download_url = f"http://localhost:9000/animation/download/{test_file}"
    print(f"🔗 下载URL: {download_url}")
    
    try:
        response = requests.get(download_url, timeout=30)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 下载成功!")
            print(f"📦 下载文件大小: {len(response.content)} bytes")
            
            # 验证文件大小
            if len(response.content) == original_size:
                print("✅ 文件大小匹配")
            else:
                print("⚠️  文件大小不匹配")
            
            # 检查Content-Type
            content_type = response.headers.get('content-type', '')
            print(f"📄 Content-Type: {content_type}")
            
            # 检查Content-Disposition
            content_disposition = response.headers.get('content-disposition', '')
            print(f"📎 Content-Disposition: {content_disposition}")
            
        else:
            print(f"❌ 下载失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请确保服务器正在运行: uv run uvicorn backend.app:app --host 0.0.0.0 --port 9000")
        
    except Exception as e:
        print(f"❌ 下载测试失败: {e}")


def test_health_check():
    """测试健康检查"""
    print("\n🏥 测试健康检查")
    print("=" * 20)
    
    try:
        response = requests.get("http://localhost:9000/health", timeout=10)
        if response.status_code == 200:
            print("✅ 服务器健康状态良好")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查错误: {e}")


def main():
    """主测试函数"""
    print("🧪 下载端点测试")
    print("=" * 40)
    
    # 健康检查
    test_health_check()
    
    # 下载测试
    test_download_endpoint()
    
    print("\n💡 如果下载测试通过，前端的Download按钮应该可以正常工作")


if __name__ == "__main__":
    main()
