<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>🧪 Motion Agent Download Test</h1>
    
    <div class="test-section">
        <h2>📁 Available FBX Files</h2>
        <p>Click a button to download the corresponding FBX file:</p>
        <div id="file-buttons"></div>
    </div>
    
    <div class="test-section">
        <h2>🔗 Direct Download Test</h2>
        <button onclick="testDirectDownload()">Test Direct Download</button>
        <button onclick="testApiGeneration()">Generate New Animation & Download</button>
    </div>
    
    <div class="test-section">
        <h2>📊 Test Log</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:9000';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        async function downloadFile(filename) {
            try {
                log(`🔗 Starting download: ${filename}`);
                
                const downloadUrl = `${API_BASE}/animation/download/${filename}`;
                log(`📡 Fetching: ${downloadUrl}`);
                
                const response = await fetch(downloadUrl);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                log(`✅ Response received (${response.status})`);
                
                const blob = await response.blob();
                log(`📦 Blob created (${blob.size} bytes)`);
                
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                log(`🎉 Download completed: ${filename}`);
                
            } catch (error) {
                log(`❌ Download failed: ${error.message}`);
                console.error('Download error:', error);
            }
        }
        
        async function testDirectDownload() {
            // Test with a known file
            await downloadFile('animation_default_1749100020.fbx');
        }
        
        async function testApiGeneration() {
            try {
                log('🎬 Generating new animation...');
                
                const response = await fetch(`${API_BASE}/animation/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: '向前走三步然后挥手',
                        character_id: 'web_test',
                        animator_level: 'intermediate',
                        quality_target: 'game_ready',
                        frame_rate: 30,
                        export_format: 'fbx'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`API Error: ${response.status}`);
                }
                
                const result = await response.json();
                log(`✅ Animation generated successfully`);
                
                if (result.success && result.fbx_file_path) {
                    const filename = result.fbx_file_path.split('/').pop();
                    log(`📁 FBX file: ${filename}`);
                    
                    // Auto-download the generated file
                    await downloadFile(filename);
                } else {
                    log(`❌ No FBX file in response: ${JSON.stringify(result)}`);
                }
                
            } catch (error) {
                log(`❌ API generation failed: ${error.message}`);
                console.error('API error:', error);
            }
        }
        
        async function loadAvailableFiles() {
            try {
                // This would require a new API endpoint to list files
                // For now, we'll just show some test buttons
                const fileButtons = document.getElementById('file-buttons');
                
                const testFiles = [
                    'animation_default_1749100020.fbx',
                    'test_animation_1749095407.fbx'
                ];
                
                testFiles.forEach(filename => {
                    const button = document.createElement('button');
                    button.textContent = `Download ${filename}`;
                    button.onclick = () => downloadFile(filename);
                    fileButtons.appendChild(button);
                });
                
                log('📋 Test buttons loaded');
                
            } catch (error) {
                log(`❌ Failed to load files: ${error.message}`);
            }
        }
        
        // Initialize
        window.onload = () => {
            log('🚀 Download test page loaded');
            loadAvailableFiles();
        };
    </script>
</body>
</html>
