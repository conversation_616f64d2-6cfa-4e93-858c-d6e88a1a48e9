"""
专业游戏动画师API端点
Professional Game Animator API Endpoints
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any
from loguru import logger

from .models import AnimationRequest, AnimationResponse
from .professional_pipeline import ProfessionalAnimationPipeline

# 创建路由器
router = APIRouter(prefix="/animation", tags=["Professional Animation"])

# 初始化管道
animation_pipeline = ProfessionalAnimationPipeline()


@router.post("/generate", response_model=AnimationResponse)
async def generate_professional_animation(request: AnimationRequest):
    """
    生成专业游戏动画
    
    支持的输入示例：
    - "生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步"
    - "角色快速冲刺，然后跳跃攻击，最后防御姿态"
    - "慢慢走向前方，挥手打招呼，然后坐下休息"
    """
    logger.info(f"Received professional animation request: {request.text}")
    
    try:
        # 处理动画请求
        response = await animation_pipeline.process_animation_request(request)
        
        if response.success:
            logger.success(f"Professional animation generated successfully")
        else:
            logger.error(f"Animation generation failed: {response.error_message}")
        
        return response
        
    except Exception as e:
        logger.exception(f"Error in professional animation generation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate-async")
async def generate_animation_async(request: AnimationRequest, background_tasks: BackgroundTasks):
    """
    异步生成动画（适用于复杂动画）
    """
    logger.info(f"Received async animation request: {request.text}")
    
    # 添加后台任务
    background_tasks.add_task(process_animation_background, request)
    
    return {
        "message": "Animation generation started",
        "request_id": f"anim_{hash(request.text)}",
        "estimated_time": "2-5 minutes"
    }


async def process_animation_background(request: AnimationRequest):
    """
    后台处理动画生成
    """
    try:
        response = await animation_pipeline.process_animation_request(request)
        logger.info(f"Background animation generation completed: {response.success}")
        
        # 这里可以添加通知机制，比如发送邮件或推送通知
        
    except Exception as e:
        logger.error(f"Background animation generation failed: {e}")


@router.get("/presets")
async def get_animation_presets():
    """
    获取可用的动画预设
    """
    return {
        "locomotion": [
            "walk", "run", "sprint", "jump", "hop", "skip"
        ],
        "acrobatic": [
            "backflip", "frontflip", "sideflip", "roll", "cartwheel"
        ],
        "combat": [
            "punch", "kick", "slash", "block", "dodge", "parry"
        ],
        "gestures": [
            "wave", "point", "clap", "thumbs_up", "salute"
        ],
        "expressions": [
            "smile", "frown", "surprise", "anger", "sad", "happy"
        ],
        "idle": [
            "breathing", "looking_around", "fidget", "stretch"
        ]
    }





@router.get("/quality-targets")
async def get_quality_targets():
    """
    获取质量目标说明
    """
    return {
        "prototype": {
            "description": "原型质量",
            "frame_rate": 15,
            "compression": "high",
            "use_case": "快速原型验证"
        },
        "game_ready": {
            "description": "游戏就绪质量",
            "frame_rate": 30,
            "compression": "medium", 
            "use_case": "标准游戏发布"
        },
        "cinematic": {
            "description": "电影级质量",
            "frame_rate": 60,
            "compression": "low",
            "use_case": "过场动画、宣传片"
        }
    }


@router.post("/validate-request")
async def validate_animation_request(request: AnimationRequest):
    """
    验证动画请求的可行性
    """
    try:
        # 使用NLU分析请求
        nlu_response = await animation_pipeline.nlu.process_natural_language(request)
        
        if not nlu_response.success:
            return {
                "valid": False,
                "issues": [nlu_response.error_message],
                "suggestions": ["请使用更清晰的动作描述"]
            }
        
        # 分析动作复杂度
        complexity = animation_pipeline._calculate_complexity_score(nlu_response.animation_sequence)
        
        issues = []
        suggestions = []
        
        if complexity > 80:
            issues.append("动画序列过于复杂")
            suggestions.append("考虑分解为多个简单动画")
        
        if len(nlu_response.animation_sequence.actions) > 15:
            issues.append("动作数量过多")
            suggestions.append("建议限制在10个动作以内")
        
        return {
            "valid": len(issues) == 0,
            "complexity_score": complexity,
            "estimated_duration": nlu_response.animation_sequence.total_duration,
            "action_count": len(nlu_response.animation_sequence.actions),
            "issues": issues,
            "suggestions": suggestions,
            "detected_actions": [action.name for action in nlu_response.animation_sequence.actions]
        }
        
    except Exception as e:
        logger.error(f"Error validating request: {e}")
        return {
            "valid": False,
            "issues": [str(e)],
            "suggestions": ["请检查输入格式"]
        }


@router.get("/examples")
async def get_example_requests():
    """
    获取示例请求
    """
    return {
        "simple_examples": [
            {
                "text": "向前走三步",
                "description": "简单移动动画",
                "animator_level": "junior"
            },
            {
                "text": "跳跃然后落地",
                "description": "基础跳跃动作",
                "animator_level": "junior"
            },
            {
                "text": "挥手打招呼",
                "description": "手势动画",
                "animator_level": "junior"
            }
        ],
        "intermediate_examples": [
            {
                "text": "冲刺攻击然后防御",
                "description": "战斗动作组合",
                "animator_level": "intermediate"
            },
            {
                "text": "后空翻360度",
                "description": "特技动作",
                "animator_level": "intermediate"
            },
            {
                "text": "愤怒表情然后攻击",
                "description": "表情+动作组合",
                "animator_level": "intermediate"
            }
        ],
        "complex_examples": [
            {
                "text": "生成一个后空翻720度之后，向前五步走，然后转身，并向左迈一步",
                "description": "复杂动作序列",
                "animator_level": "intermediate"
            },
            {
                "text": "角色慢慢走向敌人，突然加速冲刺，跳跃攻击，落地后翻滚闪避，最后摆出防御姿态",
                "description": "完整战斗序列",
                "animator_level": "intermediate"
            }
        ]
    }


@router.get("/health")
async def animation_health_check():
    """
    动画系统健康检查
    """
    try:
        # 检查Blender可用性
        blender_available = os.path.exists(animation_pipeline.blender_path)
        
        # 检查输出目录
        output_dir_writable = os.access(animation_pipeline.output_dir, os.W_OK)
        
        return {
            "status": "healthy" if blender_available and output_dir_writable else "degraded",
            "blender_available": blender_available,
            "blender_path": animation_pipeline.blender_path,
            "output_directory": animation_pipeline.output_dir,
            "output_writable": output_dir_writable,
            "nlu_ready": animation_pipeline.nlu is not None,
            "junior_animator_ready": animation_pipeline.junior_animator is not None,
            "intermediate_animator_ready": animation_pipeline.intermediate_animator is not None
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
