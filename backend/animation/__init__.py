"""
专业游戏动画师模块
Professional Game Animator Module
"""

from .models import (
    AnimationType, BodyPart, Direction, AnimationIntensity,
    AnimatorAction, AnimationSequence, MotionCaptureData,
    AnimationRequest, AnimationResponse, BlenderExportConfig
)

from .professional_nlu import ProfessionalAnimatorNLU
from .animator_functions import UnifiedAnimator, JuniorAnimator, IntermediateAnimator
from .professional_pipeline import ProfessionalAnimationPipeline
from .api import router as animation_router

__all__ = [
    # 数据模型
    "AnimationType",
    "BodyPart", 
    "Direction",
    "AnimationIntensity",
    "AnimatorAction",
    "AnimationSequence",
    "MotionCaptureData",
    "AnimationRequest",
    "AnimationResponse",
    "BlenderExportConfig",
    
    # 核心组件
    "ProfessionalAnimatorNLU",
    "UnifiedAnimator",
    "JuniorAnimator",  # Legacy compatibility
    "IntermediateAnimator",  # Legacy compatibility
    "ProfessionalAnimationPipeline",
    
    # API路由
    "animation_router"
]
